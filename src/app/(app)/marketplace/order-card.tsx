'use client';

import { BaseOrderCard } from '@/components/shared/base-order-card';
import { PriceButton } from '@/components/shared/price-display';
import { SecondaryMarketBadge } from '@/components/shared/secondary-market-badge';
import type { CollectionEntity, OrderEntity } from '@/constants/core.constants';
import { isSecondaryMarketOrder } from '@/utils/secondary-market-utils';

interface OrderCardProps {
  order: OrderEntity;
  collection: CollectionEntity | undefined;
  onClick: () => void;
}

export function OrderCard({ order, collection, onClick }: OrderCardProps) {
  const isSecondary = isSecondaryMarketOrder(order);

  return (
    <BaseOrderCard
      imageBadge={isSecondary && <SecondaryMarketBadge className="mt-2 ml-2" />}
      order={order}
      collection={collection}
      onClick={onClick}
    >
      <PriceButton
        amount={isSecondary ? order.secondaryMarketPrice || 0 : order.price}
      />
    </BaseOrderCard>
  );
}
