'use client';

import { TabsList } from '@telegram-apps/telegram-ui';
import { TabsItem } from '@telegram-apps/telegram-ui/dist/components/Navigation/TabsList/components/TabsItem/TabsItem';

import type { TabType } from '../hooks/use-marketplace-orders';

interface MarketplaceTabsProps {
  activeTab: TabType;
  onTabChange: (tab: TabType) => void;
}

export const MarketplaceTabs = ({
  activeTab,
  onTabChange,
}: MarketplaceTabsProps) => {
  return (
    <TabsList className="grid w-full grid-cols-2 gap-0!">
      <TabsItem
        selected={activeTab === 'buyers'}
        onClick={() => onTabChange('buyers')}
      >
        For Buyers
      </TabsItem>
      <TabsItem
        selected={activeTab === 'sellers'}
        onClick={() => onTabChange('sellers')}
      >
        For Sellers
      </TabsItem>
    </TabsList>
  );
};
