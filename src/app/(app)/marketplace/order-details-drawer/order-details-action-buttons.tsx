import { Loader2 } from 'lucide-react';
import type { ReactNode } from 'react';

import { Button } from '@/components/ui/button';

interface OrderDetailsActionButtonsProps {
  primaryAction: {
    label: ReactNode;
    onClick: () => void;
    loading: boolean;
    disabled?: boolean;
  };
  secondaryAction?: {
    label: ReactNode;
    onClick: () => void;
    disabled?: boolean;
  };
  onClose: () => void;
  actionLoading: boolean;
}

export function OrderDetailsActionButtons({
  primaryAction,
  secondaryAction,
  onClose,
  actionLoading,
}: OrderDetailsActionButtonsProps) {
  return (
    <div className="space-y-3 pt-4">
      <Button
        onClick={primaryAction.onClick}
        disabled={primaryAction.loading || primaryAction.disabled}
        className="w-full h-12 bg-[#6ab2f2] hover:bg-[#6ab2f2]/90 text-white border-0 rounded-2xl"
      >
        {primaryAction.loading ? (
          <>
            <Loader2 className="w-5 h-5 animate-spin mr-2" />
            Processing...
          </>
        ) : (
          primaryAction.label
        )}
      </Button>

      {secondaryAction && (
        <Button
          onClick={secondaryAction.onClick}
          disabled={secondaryAction.disabled || actionLoading}
          variant="outline"
          className="w-full h-12 border-[#6ab2f2] text-[#6ab2f2] hover:bg-[#6ab2f2]/10 bg-transparent rounded-2xl"
        >
          {secondaryAction.label}
        </Button>
      )}

      <Button
        variant="outline"
        onClick={onClose}
        className="w-full h-12 border-[#3a4a5c] text-[#f5f5f5] hover:bg-[#232e3c]/50 bg-transparent rounded-2xl"
        disabled={actionLoading}
      >
        Close
      </Button>
    </div>
  );
}
