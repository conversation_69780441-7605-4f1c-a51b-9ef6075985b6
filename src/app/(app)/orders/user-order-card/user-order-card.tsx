'use client';

import { OrderImage } from '@/components/shared/order-image';
import { Card, CardContent } from '@/components/ui/card';
import { OrderDeadlineTimer } from '@/components/ui/order/order-deadline-timer';
import { OrderFreezeWarning } from '@/components/ui/order/order-freeze-warning';
import type { OrderEntity, UserType } from '@/constants/core.constants';
import { OrderStatus } from '@/constants/core.constants';
import { useOrderTimers } from '@/hooks/use-order-timers';
import { useRootContext } from '@/root-context';

import { UserOrderCardHeader } from './user-order-card-header';
import { UserOrderCardInfo } from './user-order-card-info';

interface UserOrderCardProps {
  order: OrderEntity;
  userType: UserType;
  onClick: () => void;
}

export function UserOrderCard({
  order,
  userType,
  onClick,
}: UserOrderCardProps) {
  const { collections } = useRootContext();
  const collection =
    collections.find((c) => c.id === order.collectionId) || null;
  const { isFreezed } = useOrderTimers({ order, collection });

  const showDeadlineTimer =
    order.status === OrderStatus.PAID ||
    order.status === OrderStatus.GIFT_SENT_TO_RELAYER;

  const showFreezeWarning =
    order.status === OrderStatus.PAID && isFreezed && userType === 'seller';

  return (
    <Card
      className="bg-[#232e3c] border-[#3a4a5c] hover:bg-[#2a3441] transition-colors cursor-pointer group"
      onClick={onClick}
    >
      <CardContent className="p-2 flex flex-col h-full">
        <div className="relative mb-1">
          <OrderImage
            order={order}
            collection={collection}
            className="aspect-square relative rounded-lg overflow-hidden bg-[#17212b]"
          >
            <UserOrderCardHeader order={order} userType={userType} />
          </OrderImage>
        </div>

        <UserOrderCardInfo order={order} collection={collection} />

        {showDeadlineTimer && (
          <OrderDeadlineTimer
            {...{
              order,
              collection,
              userType,
            }}
            className={showFreezeWarning ? 'mb-1' : ''}
          />
        )}

        {showFreezeWarning && (
          <OrderFreezeWarning
            order={order}
            userType={userType}
            isFreezed={isFreezed}
            collection={collection}
          />
        )}
      </CardContent>
    </Card>
  );
}
