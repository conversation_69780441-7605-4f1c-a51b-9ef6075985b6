import { TonPriceDisplay } from '@/components/shared/ton-price-display';
import type { OrderEntity } from '@/constants/core.constants';

interface UserOrderSellerEarningsSectionProps {
  order: OrderEntity;
}

export function UserOrderSellerEarningsSection({
  order,
}: UserOrderSellerEarningsSectionProps) {
  if (
    !order.reseller_earnings_for_seller ||
    order.reseller_earnings_for_seller <= 0
  ) {
    return null;
  }

  return (
    <div className="pt-6 border-t border-[#3a4a5c]/30">
      <h3 className="text-lg font-semibold text-[#f5f5f5] mb-3">
        Resale Earnings
      </h3>
      <div className="bg-[#232e3c] rounded-lg p-4">
        <div className="flex items-center justify-between">
          <span className="text-[#708499] text-sm">
            Total earnings from resales:
          </span>
          <TonPriceDisplay
            amount={order.reseller_earnings_for_seller || 0}
            size={20}
            className="text-xl font-bold text-[#6ab2f2]"
            showUnit
          />
        </div>
        <p className="text-xs text-[#708499] mt-2">
          This amount represents your accumulated earnings from each time this
          order is resold on the secondary market.
        </p>
      </div>
    </div>
  );
}
